import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

titanic_data = pd.read_csv('train.csv')

titanic_data

import seaborn as sns

sns.heatmap(titanic_data.corr(numeric_only=True),cmap="YlGnBu")
plt.show()

from sklearn.model_selection import StratifiedShuffleSplit

split = StratifiedShuffleSplit(n_splits=1,test_size=0.2)
for train_indices,test_indices in split.split(titanic_data,titanic_data[["Survived","Pclass","Sex"]]):
    start_train_set = titanic_data.loc[train_indices]
    start_test_set = titanic_data.loc[test_indices]

from sklearn.base import BaseEstimator,TransformerMixin
from sklearn.impute import SimpleImputer

class AgeImputer(BaseEstimator,TransformerMixin):
    def fit(self,X,y=None):
        return self
        
    def transform(self,X):
        imputer = SimpleImputer(strategy="mean")
        X['Age'] = imputer.fit_transform(X[['Age']])
        return X

from sklearn.preprocessing import OneHotEncoder

class FeatureEncoder(BaseEstimator,TransformerMixin):
    def fit(self,X,y=None):
        return self

    def transform(self, X):
        encoder = OneHotEncoder()

        matrix = encoder.fit_transform(X[['Embarked']]).toarray()
        column_names = ["C", "S", "Q", "N"]
        for i in range(len(matrix.T)):
            X[column_names[i]] = matrix.T[i]

        matrix = encoder.fit_transform(X[['Sex']]).toarray()
        column_names = ["Female", "Male"]
        for i in range(len(matrix.T)):
            X[column_names[i]] = matrix.T[i]
        return X

class FeatureDropper(BaseEstimator,TransformerMixin):
    def fit(self,X,y=None):
        return self

    def transform(self,X):
        X = X.drop(columns=["Embarked","Name","Ticket","Cabin","Sex","N"],axis=1,errors="ignore")
        return X

from sklearn.pipeline import Pipeline

pipeline = Pipeline([("ageimputer",AgeImputer()),
                     ("featureencoder",FeatureEncoder()),
                     ("featuredropper",FeatureDropper())])

start_train_set = pipeline.fit_transform(start_train_set)

start_train_set

start_train_set.info()

from sklearn.preprocessing import StandardScaler

X = start_train_set.drop(columns=["Survived"],axis=1,errors="ignore")
y = start_train_set["Survived"]

scaler = StandardScaler()
X_data = scaler.fit_transform(X)
y_data = y.to_numpy()

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import GridSearchCV

clf = RandomForestClassifier(random_state=42)

param_grid = {
    'n_estimators': [10,100, 200, 500],
   'max_depth': [5, 10],
   'min_samples_split': [2, 3, 4]
   
}
grid_search = GridSearchCV(clf, param_grid, cv=3, scoring='accuracy', verbose=1, return_train_score=True)
grid_search.fit(X_data, y_data)

final_clf = grid_search.best_estimator_

final_clf

start_test_set = pipeline.transform(start_test_set)


start_test_set

X_test = start_test_set.drop(columns=["Survived"],axis=1,errors="ignore")
y_test = start_test_set["Survived"]

scaler = StandardScaler()
X_test = scaler.fit_transform(X_test)
y_test = y_test.to_numpy()


final_clf.score(X_test, y_test)

final_data = pipeline.transform(titanic_data)
final_data

X_final = final_data.drop(columns=["Survived"],axis=1,errors="ignore")
y_final = final_data["Survived"]

scaler = StandardScaler()
X_final = scaler.fit_transform(X_final)
y_final = y_final.to_numpy()

prod_clf = RandomForestClassifier()

param_grid = {
    'n_estimators': [10,100, 200, 500],
   'max_depth': [None,5, 10],
   'min_samples_split': [2, 3, 4]
   
}
grid_search = GridSearchCV(prod_clf, param_grid, cv=3, scoring='accuracy', return_train_score=True)
grid_search.fit(X_final, y_final)

prod_final_clf = grid_search.best_estimator_
prod_final_clf

titanic_test_data = pd.read_csv('test.csv')
final_test_data = pipeline.transform(titanic_test_data)
final_test_data.head()

X_final_test = final_test_data
X_final_test = X_final_test.fillna(method='ffill')

scaler = StandardScaler()
X_data_final_test = scaler.fit_transform(X_final_test)

predictions = prod_final_clf.predict(X_data_final_test)
print(predictions)

fianl_df = pd.DataFrame(titanic_test_data["PassengerId"])
fianl_df["Survived"] = predictions
fianl_df.to_csv("submission.csv", index=False)