{"cells": [{"cell_type": "code", "execution_count": 3, "id": "d91c5337-b4db-47a6-886e-033758e9587d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 5, "id": "39de75c6-8686-4f36-8137-9679cacd9788", "metadata": {}, "outputs": [], "source": ["titanic_data = pd.read_csv('train.csv')"]}, {"cell_type": "code", "execution_count": 7, "id": "1f408436-6815-4a8f-938c-12017ca8131b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>A/5 21171</td>\n", "      <td>7.2500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>PC 17599</td>\n", "      <td>71.2833</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "      <td>7.9250</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>35.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>113803</td>\n", "      <td>53.1000</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>373450</td>\n", "      <td>8.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>887</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>27.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>211536</td>\n", "      <td>13.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>888</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>19.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>112053</td>\n", "      <td>30.0000</td>\n", "      <td>B42</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>889</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, <PERSON><PERSON> \"<PERSON>\"</td>\n", "      <td>female</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>W./C. 6607</td>\n", "      <td>23.4500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>889</th>\n", "      <td>890</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>111369</td>\n", "      <td>30.0000</td>\n", "      <td>C148</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>890</th>\n", "      <td>891</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>32.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>370376</td>\n", "      <td>7.7500</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>891 rows × 12 columns</p>\n", "</div>"], "text/plain": ["     PassengerId  Survived  Pclass  \\\n", "0              1         0       3   \n", "1              2         1       1   \n", "2              3         1       3   \n", "3              4         1       1   \n", "4              5         0       3   \n", "..           ...       ...     ...   \n", "886          887         0       2   \n", "887          888         1       1   \n", "888          889         0       3   \n", "889          890         1       1   \n", "890          891         0       3   \n", "\n", "                                                  Name     Sex   Age  SibSp  \\\n", "0                              <PERSON><PERSON>, Mr. <PERSON>    male  22.0      1   \n", "1    C<PERSON>ngs, Mrs. <PERSON> (Florence Briggs Th...  female  38.0      1   \n", "2                               <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female  26.0      0   \n", "3         <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female  35.0      1   \n", "4                             <PERSON>, Mr. <PERSON>    male  35.0      0   \n", "..                                                 ...     ...   ...    ...   \n", "886                              <PERSON><PERSON>, <PERSON><PERSON>    male  27.0      0   \n", "887                       <PERSON>, <PERSON><PERSON>  female  19.0      0   \n", "888           <PERSON>, <PERSON><PERSON> \"<PERSON>\"  female   NaN      1   \n", "889                              <PERSON><PERSON>, Mr. <PERSON>    male  26.0      0   \n", "890                                <PERSON><PERSON>, Mr. <PERSON>    male  32.0      0   \n", "\n", "     Parch            Ticket     Fare Cabin Embarked  \n", "0        0         A/5 21171   7.2500   NaN        S  \n", "1        0          PC 17599  71.2833   C85        C  \n", "2        0  STON/O2. 3101282   7.9250   NaN        S  \n", "3        0            113803  53.1000  C123        S  \n", "4        0            373450   8.0500   NaN        S  \n", "..     ...               ...      ...   ...      ...  \n", "886      0            211536  13.0000   NaN        S  \n", "887      0            112053  30.0000   B42        S  \n", "888      2        W./C. 6607  23.4500   NaN        S  \n", "889      0            111369  30.0000  C148        C  \n", "890      0            370376   7.7500   NaN        Q  \n", "\n", "[891 rows x 12 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_data"]}, {"cell_type": "code", "execution_count": 11, "id": "fe45c085-6f99-40a6-bf14-ba5d889f8660", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "\n", "sns.heatmap(titanic_data.corr(numeric_only=True),cmap=\"YlGnBu\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 15, "id": "bb1b9a3b-3ed3-4ee2-8fa4-4b80f7bd4b82", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import StratifiedShuffleSplit\n", "\n", "split = StratifiedShuffleSplit(n_splits=1,test_size=0.2)\n", "for train_indices,test_indices in split.split(titanic_data,titanic_data[[\"Survived\",\"Pclass\",\"Sex\"]]):\n", "    start_train_set = titanic_data.loc[train_indices]\n", "    start_test_set = titanic_data.loc[test_indices]"]}, {"cell_type": "code", "execution_count": null, "id": "4f47230b-e769-41c8-815b-da3c7f5113b7", "metadata": {}, "outputs": [], "source": ["from sklearn.base import BaseEstimator,TransformerMixin\n", "from sklearn.impute import SimpleImputer\n", "\n", "class AgeImputer(BaseEstimator,TransformerMixin):\n", "    def fit(self,X,y=None):\n", "        return self\n", "        \n", "    def transform(self,X):\n", "        imputer = SimpleImputer(strategy=\"mean\")\n", "        X['Age'] = imputer.fit_transform(X[['Age']])\n", "        return X"]}, {"cell_type": "code", "execution_count": null, "id": "e57f6e2c-5e30-4996-8bb1-9ae2b61254ed", "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "\n", "class FeatureEncoder(BaseEstimator,TransformerMixin):\n", "    def fit(self,X,y=None):\n", "        return self\n", "\n", "    def transform(self,x):\n", "        encoder = OneHotEncoder()\n", "        \n", "        matrix = encoder.fit_transform(X[['Embarked']]).toarray()\n", "        column_names = [\"C\",\"S\",\"Q\",\"N\"]\n", "        for i in range(len(matrix.T)):\n", "            X[column_name[i]] = matrix.T[i]\n", "            \n", "        matrix = encoder.fit_transform(X[['Sex']]).toarray()\n", "        column_names = [\"Female\",\"Male\"]\n", "        for i in range(len(matrix.T)):\n", "            X[column_names[i]] = matrix.T[i]\n", "        return X"]}, {"cell_type": "code", "execution_count": null, "id": "9c031b2c-b559-4a49-aa18-294c17b6130d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}